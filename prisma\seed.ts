import { PrismaClient, FeeType, PartnerType, PartnershipTier } from '@prisma/client';
import { hash } from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('Starting database seed...');

  // Get superadmin credentials from environment variables
  const superadminEmail = process.env.DEFAULT_SUPERADMIN_EMAIL || '<EMAIL>';
  const superadminPassword = process.env.DEFAULT_SUPERADMIN_PASSWORD || 'SuperAdmin@1234567';
  const superadminName = process.env.DEFAULT_SUPERADMIN_NAME || 'System Super Administrator';

  // Get regular admin credentials from environment variables
  const adminEmail = process.env.DEFAULT_ADMIN_EMAIL || '<EMAIL>';
  const adminPassword = process.env.DEFAULT_ADMIN_PASSWORD || 'Admin@123456';
  const adminName = process.env.DEFAULT_ADMIN_NAME || 'System Administrator';

  console.log(`Checking for existing superadmin user with email: ${superadminEmail}`);

  // Check if superadmin user already exists
  const existingSuperadmin = await prisma.user.findFirst({
    where: {
      email: superadminEmail,
      role: 'SUPERADMIN'
    }
  });

  if (existingSuperadmin) {
    console.log('Superadmin user already exists:', existingSuperadmin.email);
  } else {
    console.log('No superadmin user found. Creating default superadmin user...');

    // Hash the password
    const hashedSuperadminPassword = await hash(superadminPassword, 12);

    // Create the superadmin user
    const superadmin = await prisma.user.create({
      data: {
        email: superadminEmail,
        name: superadminName,
        password: hashedSuperadminPassword,
        role: 'SUPERADMIN',
        emailVerified: new Date(),
      }
    });

    console.log('Default superadmin user created:', superadmin.email);
  }

  console.log(`Checking for existing admin user with email: ${adminEmail}`);

  // Check if user with admin email exists (regardless of role)
  const existingUser = await prisma.user.findUnique({
    where: {
      email: adminEmail
    }
  });

  if (existingUser) {
    console.log('User with admin email already exists:', existingUser.email);

    // Check if the user is already an ADMIN
    if (existingUser.role === 'ADMIN') {
      console.log('User already has ADMIN role');
    } else {
      console.log(`Updating user role from ${existingUser.role} to ADMIN...`);

      // Update the user's role to ADMIN
      const updatedUser = await prisma.user.update({
        where: { id: existingUser.id },
        data: { role: 'ADMIN' }
      });

      console.log('User role updated to ADMIN:', updatedUser.email);
    }
  } else {
    console.log('No user with admin email found. Creating default admin user...');

    // Hash the password
    const hashedAdminPassword = await hash(adminPassword, 12);

    // Create the admin user
    const admin = await prisma.user.create({
      data: {
        email: adminEmail,
        name: adminName,
        password: hashedAdminPassword,
        role: 'ADMIN',
        emailVerified: new Date(),
      }
    });

    console.log('Default admin user created:', admin.email);
  }

  // Create default fee configurations
  console.log('Setting up default fee configurations...');

  try {
    // 1. Platform commission fee (6% of ticket sales)
    await prisma.feeConfiguration.upsert({
      where: { id: 'platform-commission-fee' },
      update: {},
      create: {
        id: 'platform-commission-fee',
        name: 'Platform Commission',
        description: 'Platform commission fee for ticket sales',
        feeType: FeeType.PLATFORM_COMMISSION,
        value: 6.0, // 6%
        isPercentage: true,
        isActive: true,
        appliesTo: ['ORGANIZER'],
        transactionType: ['TICKET_SALE'],
        effectiveFrom: new Date(),
      },
    });

    // 2. Processing fee for organizers (3.5%)
    await prisma.feeConfiguration.upsert({
      where: { id: 'organizer-processing-fee' },
      update: {},
      create: {
        id: 'organizer-processing-fee',
        name: 'Organizer Processing Fee',
        description: 'Payment processing fee for organizers',
        feeType: FeeType.PROCESSING_FEE,
        value: 3.5, // 3.5%
        isPercentage: true,
        isActive: true,
        appliesTo: ['ORGANIZER'],
        transactionType: ['TICKET_SALE', 'PROCESSING_FEE'],
        effectiveFrom: new Date(),
      },
    });

    // 3. Processing fee for vendors (3.5%)
    await prisma.feeConfiguration.upsert({
      where: { id: 'vendor-processing-fee' },
      update: {},
      create: {
        id: 'vendor-processing-fee',
        name: 'Vendor Processing Fee',
        description: 'Payment processing fee for vendors',
        feeType: FeeType.PROCESSING_FEE,
        value: 3.5, // 3.5%
        isPercentage: true,
        isActive: true,
        appliesTo: ['VENDOR'],
        transactionType: ['VENDOR_SALE', 'PROCESSING_FEE'],
        effectiveFrom: new Date(),
      },
    });

    // 4. POS rental fee for vendors (K1000 flat fee)
    await prisma.feeConfiguration.upsert({
      where: { id: 'pos-rental-fee' },
      update: {},
      create: {
        id: 'pos-rental-fee',
        name: 'POS Rental Fee',
        description: 'Flat fee for POS device rental',
        feeType: FeeType.POS_RENTAL_FEE,
        value: 1000, // K1000
        isPercentage: false,
        isActive: true,
        appliesTo: ['VENDOR'],
        transactionType: ['POS_RENTAL_FEE'],
        effectiveFrom: new Date(),
      },
    });

    console.log('Default fee configurations created successfully');
  } catch (error) {
    console.error('Error creating fee configurations:', error);
  }

  // Create test users with different subscription tiers
  console.log('Creating test users with different subscription tiers...');

  try {
    // Create test organizer with BASIC subscription
    const basicOrganizerEmail = '<EMAIL>';
    const existingBasicOrganizer = await prisma.user.findUnique({
      where: { email: basicOrganizerEmail }
    });

    if (!existingBasicOrganizer) {
      const hashedPassword = await hash('Password123', 12);
      const basicOrganizer = await prisma.user.create({
        data: {
          email: basicOrganizerEmail,
          name: 'Basic Organizer',
          password: hashedPassword,
          role: 'ORGANIZER',
          emailVerified: new Date(),
          subscriptionTier: 'BASIC' as const,
          subscriptionStartDate: new Date(),
          subscriptionEndDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        }
      });

      // Create subscription history record
      await prisma.subscriptionHistory.create({
        data: {
          userId: basicOrganizer.id,
          tier: 'BASIC' as const,
          startDate: new Date(),
          endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
          amount: 29.99,
          billingCycle: 'monthly',
          status: 'ACTIVE',
        }
      });

      console.log('Created Basic Organizer:', basicOrganizer.email);
    } else {
      console.log('Basic Organizer already exists:', basicOrganizerEmail);
    }

    // Create test organizer with PREMIUM subscription
    const premiumOrganizerEmail = '<EMAIL>';
    const existingPremiumOrganizer = await prisma.user.findUnique({
      where: { email: premiumOrganizerEmail }
    });

    if (!existingPremiumOrganizer) {
      const hashedPassword = await hash('Password123', 12);
      const premiumOrganizer = await prisma.user.create({
        data: {
          email: premiumOrganizerEmail,
          name: 'Premium Organizer',
          password: hashedPassword,
          role: 'ORGANIZER',
          emailVerified: new Date(),
          subscriptionTier: 'PREMIUM' as const,
          subscriptionStartDate: new Date(),
          subscriptionEndDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        }
      });

      // Create subscription history record
      await prisma.subscriptionHistory.create({
        data: {
          userId: premiumOrganizer.id,
          tier: 'PREMIUM' as const,
          startDate: new Date(),
          endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
          amount: 79.99,
          billingCycle: 'monthly',
          status: 'ACTIVE',
        }
      });

      console.log('Created Premium Organizer:', premiumOrganizer.email);
    } else {
      console.log('Premium Organizer already exists:', premiumOrganizerEmail);
    }

    // Create test organizer with ELITE subscription
    const eliteOrganizerEmail = '<EMAIL>';
    const existingEliteOrganizer = await prisma.user.findUnique({
      where: { email: eliteOrganizerEmail }
    });

    if (!existingEliteOrganizer) {
      const hashedPassword = await hash('Password123', 12);
      const eliteOrganizer = await prisma.user.create({
        data: {
          email: eliteOrganizerEmail,
          name: 'Elite Organizer',
          password: hashedPassword,
          role: 'ORGANIZER',
          emailVerified: new Date(),
          subscriptionTier: 'ELITE' as const,
          subscriptionStartDate: new Date(),
          subscriptionEndDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        }
      });

      // Create subscription history record
      await prisma.subscriptionHistory.create({
        data: {
          userId: eliteOrganizer.id,
          tier: 'ELITE' as const,
          startDate: new Date(),
          endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
          amount: 199.99,
          billingCycle: 'monthly',
          status: 'ACTIVE',
        }
      });

      console.log('Created Elite Organizer:', eliteOrganizer.email);
    } else {
      console.log('Elite Organizer already exists:', eliteOrganizerEmail);
    }

    // Create test organizer with FREE subscription
    const freeOrganizerEmail = '<EMAIL>';
    const existingFreeOrganizer = await prisma.user.findUnique({
      where: { email: freeOrganizerEmail }
    });

    if (!existingFreeOrganizer) {
      const hashedPassword = await hash('Password123', 12);
      const freeOrganizer = await prisma.user.create({
        data: {
          email: freeOrganizerEmail,
          name: 'Free Organizer',
          password: hashedPassword,
          role: 'ORGANIZER',
          emailVerified: new Date(),
          subscriptionTier: 'NONE' as const,
        }
      });

      console.log('Created Free Organizer:', freeOrganizer.email);
    } else {
      console.log('Free Organizer already exists:', freeOrganizerEmail);
    }

    console.log('Test users with subscription tiers created successfully');
  } catch (error) {
    console.error('Error creating test users with subscription tiers:', error);
  }

  // Seed partners and promotions
  await seedPartners();

  // Seed Elite Communication System
  await seedEliteCommunicationSystem();

  console.log('Database seed completed.');
}

async function seedPartners() {
  console.log('Starting to seed partners...');

  // Check if partners already exist
  const partnerCount = await prisma.partner.count();

  if (partnerCount > 0) {
    console.log(`Partners already exist (${partnerCount} found). Skipping seeding.`);
    return;
  }

  // Create test users for partners if they don't exist
  const users = [
    {
      name: 'Grand Hotel',
      email: '<EMAIL>',
      password: 'Password123!',
      role: 'USER',
    },
    {
      name: 'Tasty Restaurant',
      email: '<EMAIL>',
      password: 'Password123!',
      role: 'USER',
    },
    {
      name: 'Downtown Bar',
      email: '<EMAIL>',
      password: 'Password123!',
      role: 'USER',
    },
    {
      name: 'Nightlife Club',
      email: '<EMAIL>',
      password: 'Password123!',
      role: 'USER',
    },
  ];

  for (const userData of users) {
    const existingUser = await prisma.user.findUnique({
      where: { email: userData.email },
    });

    if (!existingUser) {
      const hashedPassword = await hash(userData.password, 10);
      await prisma.user.create({
        data: {
          name: userData.name,
          email: userData.email,
          password: hashedPassword,
          role: userData.role as any,
        },
      });
      console.log(`Created user: ${userData.email}`);
    } else {
      console.log(`User already exists: ${userData.email}`);
    }
  }

  // Get the created users
  const hotelUser = await prisma.user.findUnique({ where: { email: '<EMAIL>' } });
  const restaurantUser = await prisma.user.findUnique({ where: { email: '<EMAIL>' } });
  const barUser = await prisma.user.findUnique({ where: { email: '<EMAIL>' } });
  const nightclubUser = await prisma.user.findUnique({ where: { email: '<EMAIL>' } });

  if (!hotelUser || !restaurantUser || !barUser || !nightclubUser) {
    throw new Error('Failed to create or find users');
  }

  // Create partners
  const partners = [
    {
      userId: hotelUser.id,
      businessName: 'Grand Hotel',
      partnerType: PartnerType.HOTEL,
      tier: PartnershipTier.PREMIUM,
      description: 'A luxurious hotel in the heart of the city with top-notch amenities and services.',
      address: '123 Main Street',
      city: 'Lusaka',
      province: 'Lusaka',
      postalCode: '10101',
      country: 'Zambia',
      contactName: 'John Manager',
      contactEmail: '<EMAIL>',
      contactPhone: '+260 97 1234567',
      website: 'https://grandhotel.example.com',
      amenities: ['Free Wi-Fi', 'Swimming Pool', 'Restaurant', 'Gym', 'Conference Rooms'],
      priceRange: '$$$',
      isVerified: true,
      featured: true,
      acceptsNfcPayments: true,
      commissionRate: 5.0,
    },
    {
      userId: restaurantUser.id,
      businessName: 'Tasty Restaurant',
      partnerType: PartnerType.RESTAURANT,
      tier: PartnershipTier.BASIC,
      description: 'A family-friendly restaurant serving delicious local and international cuisine.',
      address: '456 Food Street',
      city: 'Lusaka',
      province: 'Lusaka',
      postalCode: '10102',
      country: 'Zambia',
      contactName: 'Sarah Chef',
      contactEmail: '<EMAIL>',
      contactPhone: '+260 97 2345678',
      website: 'https://tastyrestaurant.example.com',
      amenities: ['Outdoor Seating', 'Private Dining', 'Takeaway', 'Delivery'],
      priceRange: '$$',
      isVerified: true,
      featured: false,
      acceptsNfcPayments: true,
      commissionRate: 4.5,
    },
    {
      userId: barUser.id,
      businessName: 'Downtown Bar',
      partnerType: PartnerType.BAR,
      tier: PartnershipTier.ELITE,
      description: 'A trendy bar with a wide selection of drinks and a vibrant atmosphere.',
      address: '789 Drink Avenue',
      city: 'Lusaka',
      province: 'Lusaka',
      postalCode: '10103',
      country: 'Zambia',
      contactName: 'Mike Bartender',
      contactEmail: '<EMAIL>',
      contactPhone: '+260 97 3456789',
      website: 'https://downtownbar.example.com',
      amenities: ['Live Music', 'Happy Hour', 'Cocktails', 'Snacks'],
      priceRange: '$$',
      isVerified: true,
      featured: true,
      acceptsNfcPayments: true,
      commissionRate: 6.0,
    },
    {
      userId: nightclubUser.id,
      businessName: 'Nightlife Club',
      partnerType: PartnerType.NIGHTCLUB,
      tier: PartnershipTier.PREMIUM,
      description: 'The hottest nightclub in town with top DJs and an amazing dance floor.',
      address: '101 Party Street',
      city: 'Lusaka',
      province: 'Lusaka',
      postalCode: '10104',
      country: 'Zambia',
      contactName: 'David DJ',
      contactEmail: '<EMAIL>',
      contactPhone: '+260 97 4567890',
      website: 'https://nightlifeclub.example.com',
      amenities: ['VIP Tables', 'Dance Floor', 'Premium Drinks', 'Security'],
      priceRange: '$$$',
      isVerified: false,
      featured: false,
      acceptsNfcPayments: false,
      commissionRate: 7.0,
    },
  ];

  const createdPartners = [];
  for (const partnerData of partners) {
    const partner = await prisma.partner.create({
      data: {
        userId: partnerData.userId,
        businessName: partnerData.businessName,
        partnerType: partnerData.partnerType,
        tier: partnerData.tier,
        description: partnerData.description,
        address: partnerData.address,
        city: partnerData.city,
        province: partnerData.province,
        postalCode: partnerData.postalCode,
        country: partnerData.country,
        contactName: partnerData.contactName,
        contactEmail: partnerData.contactEmail,
        contactPhone: partnerData.contactPhone,
        website: partnerData.website,
        amenities: partnerData.amenities,
        priceRange: partnerData.priceRange,
        isVerified: partnerData.isVerified,
        verifiedAt: partnerData.isVerified ? new Date() : null,
        featured: partnerData.featured,
        acceptsNfcPayments: partnerData.acceptsNfcPayments,
        commissionRate: partnerData.commissionRate,
      },
    });
    console.log(`Created partner: ${partnerData.businessName}`);
    createdPartners.push(partner);
  }

  // Create promotions
  const now = new Date();
  const oneMonthAgo = new Date(now);
  oneMonthAgo.setMonth(now.getMonth() - 1);

  const oneMonthFromNow = new Date(now);
  oneMonthFromNow.setMonth(now.getMonth() + 1);

  const twoMonthsFromNow = new Date(now);
  twoMonthsFromNow.setMonth(now.getMonth() + 2);

  const promotions = [
    {
      partnerId: createdPartners[0].id, // Grand Hotel
      title: '20% Off All Rooms',
      description: 'Get 20% off all room bookings for the weekend. Valid for bookings made through our website or app.',
      startDate: now,
      endDate: oneMonthFromNow,
      discountValue: 20,
      discountType: 'percentage',
      promoCode: 'WEEKEND20',
      isActive: true,
      maxUses: 100,
      currentUses: 45,
    },
    {
      partnerId: createdPartners[1].id, // Tasty Restaurant
      title: 'Free Drink with Dinner',
      description: 'Get a free drink with any dinner purchase. Valid for dine-in only.',
      startDate: oneMonthAgo,
      endDate: now,
      isActive: false,
      currentUses: 78,
    },
    {
      partnerId: createdPartners[2].id, // Downtown Bar
      title: 'Happy Hour Extended',
      description: 'Happy hour extended to 8pm every weekday. Enjoy discounted drinks and appetizers.',
      startDate: now,
      endDate: twoMonthsFromNow,
      isActive: true,
      currentUses: 120,
    },
    {
      partnerId: createdPartners[3].id, // Nightlife Club
      title: 'VIP Entry Package',
      description: 'Get VIP entry, a reserved table, and a complimentary bottle for groups of 6 or more.',
      startDate: oneMonthFromNow,
      endDate: twoMonthsFromNow,
      discountValue: 15,
      discountType: 'percentage',
      promoCode: 'VIPGROUP',
      isActive: true,
      maxUses: 50,
      currentUses: 0,
    },
  ];

  for (const promotionData of promotions) {
    await prisma.partnerPromotion.create({
      data: {
        partnerId: promotionData.partnerId,
        title: promotionData.title,
        description: promotionData.description,
        startDate: promotionData.startDate,
        endDate: promotionData.endDate,
        discountValue: promotionData.discountValue,
        discountType: promotionData.discountType as string,
        promoCode: promotionData.promoCode,
        isActive: promotionData.isActive,
        maxUses: promotionData.maxUses,
        currentUses: promotionData.currentUses,
      },
    });
    console.log(`Created promotion: ${promotionData.title}`);
  }

  console.log('Partner seeding completed successfully!');
}

/**
 * Seeds the Elite Communication System with sample data
 */
async function seedEliteCommunicationSystem() {
  console.log('Starting to seed Elite Communication System...');

  try {
    // Sample user data for Elite Communication System
    const sampleUsers = [
      {
        name: 'Sarah Chen',
        email: '<EMAIL>',
        company: 'TechCorp Solutions',
        role: 'Senior Software Engineer',
        industry: 'Technology',
        bio: 'Passionate about AI and machine learning. Looking to connect with fellow tech enthusiasts and explore collaboration opportunities.',
        interests: ['Artificial Intelligence', 'Machine Learning', 'Cloud Computing', 'Open Source'],
        networkingGoals: 'Connect with AI researchers and potential collaborators for open source projects',
        linkedinUrl: 'https://linkedin.com/in/sarahchen',
        timezone: 'America/Los_Angeles',
        tier: 'ELITE_PRO'
      },
      {
        name: 'Marcus Johnson',
        email: '<EMAIL>',
        company: 'FinancePlus',
        role: 'Investment Director',
        industry: 'Finance',
        bio: 'Experienced investment professional specializing in fintech startups. Always interested in innovative financial solutions.',
        interests: ['Fintech', 'Blockchain', 'Investment Strategy', 'Startups'],
        networkingGoals: 'Discover promising fintech startups and connect with entrepreneurs',
        linkedinUrl: 'https://linkedin.com/in/marcusjohnson',
        timezone: 'America/New_York',
        tier: 'ELITE_PRO'
      },
      {
        name: 'Dr. Emily Rodriguez',
        email: '<EMAIL>',
        company: 'HealthTech Innovations',
        role: 'Chief Medical Officer',
        industry: 'Healthcare',
        bio: 'Medical doctor turned healthcare technology executive. Focused on digital health solutions that improve patient outcomes.',
        interests: ['Digital Health', 'Telemedicine', 'Medical AI', 'Patient Care'],
        networkingGoals: 'Connect with healthcare innovators and discuss digital transformation in medicine',
        linkedinUrl: 'https://linkedin.com/in/emilyrodriguez',
        timezone: 'America/Chicago',
        tier: 'ELITE'
      },
      {
        name: 'David Kim',
        email: '<EMAIL>',
        company: 'MarketingPro Agency',
        role: 'Creative Director',
        industry: 'Marketing',
        bio: 'Award-winning creative director with 10+ years in digital marketing. Passionate about brand storytelling and user experience.',
        interests: ['Brand Strategy', 'Digital Marketing', 'UX Design', 'Content Creation'],
        networkingGoals: 'Meet potential clients and collaborate with other creative professionals',
        linkedinUrl: 'https://linkedin.com/in/davidkim',
        timezone: 'America/Los_Angeles',
        tier: 'ELITE'
      },
      {
        name: 'Lisa Thompson',
        email: '<EMAIL>',
        company: 'Independent Consultant',
        role: 'Business Strategy Consultant',
        industry: 'Consulting',
        bio: 'Independent consultant helping startups and SMEs develop growth strategies. Former McKinsey consultant.',
        interests: ['Business Strategy', 'Startups', 'Growth Hacking', 'Leadership'],
        networkingGoals: 'Find new clients and stay updated on industry trends',
        linkedinUrl: 'https://linkedin.com/in/lisathompson',
        timezone: 'America/New_York',
        tier: 'ELITE_PRO'
      },
      {
        name: 'Ahmed Hassan',
        email: '<EMAIL>',
        company: 'RetailTech Solutions',
        role: 'Product Manager',
        industry: 'Retail Technology',
        bio: 'Product manager focused on e-commerce and retail technology solutions. Experienced in agile development and user research.',
        interests: ['E-commerce', 'Product Management', 'User Research', 'Agile'],
        networkingGoals: 'Connect with other product managers and learn about emerging retail trends',
        linkedinUrl: 'https://linkedin.com/in/ahmedhassan',
        timezone: 'Europe/London',
        tier: 'ELITE'
      },
      {
        name: 'Jennifer Wu',
        email: '<EMAIL>',
        company: 'DataAnalytics Corp',
        role: 'Data Scientist',
        industry: 'Data Analytics',
        bio: 'Data scientist with expertise in predictive modeling and business intelligence. PhD in Statistics from Stanford.',
        interests: ['Data Science', 'Machine Learning', 'Statistics', 'Business Intelligence'],
        networkingGoals: 'Share knowledge and learn about new data science applications',
        linkedinUrl: 'https://linkedin.com/in/jenniferwu',
        timezone: 'America/Los_Angeles',
        tier: 'ELITE'
      },
      {
        name: 'Robert Brown',
        email: '<EMAIL>',
        company: 'Advanced Manufacturing Inc',
        role: 'Operations Director',
        industry: 'Manufacturing',
        bio: 'Operations expert with 15+ years in manufacturing. Specializing in lean processes and automation.',
        interests: ['Lean Manufacturing', 'Automation', 'Supply Chain', 'Quality Control'],
        networkingGoals: 'Learn about new manufacturing technologies and best practices',
        linkedinUrl: 'https://linkedin.com/in/robertbrown',
        timezone: 'America/Chicago',
        tier: 'BASIC'
      },
      {
        name: 'Maria Garcia',
        email: '<EMAIL>',
        company: 'Global Impact Foundation',
        role: 'Program Director',
        industry: 'Non-Profit',
        bio: 'Dedicated to social impact and sustainable development. Leading programs that address global challenges.',
        interests: ['Social Impact', 'Sustainability', 'Community Development', 'Fundraising'],
        networkingGoals: 'Connect with like-minded professionals and potential partners',
        linkedinUrl: 'https://linkedin.com/in/mariagarcia',
        timezone: 'America/New_York',
        tier: 'ELITE'
      },
      {
        name: 'James Wilson',
        email: '<EMAIL>',
        company: 'Premier Real Estate',
        role: 'Senior Broker',
        industry: 'Real Estate',
        bio: 'Commercial real estate broker with expertise in tech company relocations and office space optimization.',
        interests: ['Commercial Real Estate', 'Property Investment', 'Urban Planning', 'Architecture'],
        networkingGoals: 'Meet potential clients and stay informed about market trends',
        linkedinUrl: 'https://linkedin.com/in/jameswilson',
        timezone: 'America/Los_Angeles',
        tier: 'ELITE'
      }
    ];

    // Check if Elite Communication users already exist
    const existingEliteUsers = await prisma.user.count({
      where: {
        email: {
          in: sampleUsers.map(u => u.email)
        }
      }
    });

    if (existingEliteUsers > 0) {
      console.log(`Elite Communication users already exist (${existingEliteUsers} found). Skipping Elite Communication seeding.`);
      return;
    }

    console.log('Creating sample users for Elite Communication System...');

    // Create users
    const createdUsers = [];
    for (const userData of sampleUsers) {
      const hashedPassword = await hash('EliteUser123!', 12);
      const user = await prisma.user.create({
        data: {
          name: userData.name,
          email: userData.email,
          password: hashedPassword,
          role: 'USER',
          emailVerified: new Date(),
          image: `https://api.dicebear.com/7.x/avataaars/svg?seed=${userData.name.replace(' ', '')}`
        }
      });
      createdUsers.push({ ...user, userData });
      console.log(`Created user: ${userData.name} (${userData.email})`);
    }

    // Create sample events for Elite Communication
    console.log('Creating sample events...');

    // Get an organizer to create events
    const organizer = await prisma.user.findFirst({
      where: { role: 'ORGANIZER' }
    });

    if (!organizer) {
      console.log('No organizer found. Creating a sample organizer...');
      const hashedPassword = await hash('Organizer123!', 12);
      const newOrganizer = await prisma.user.create({
        data: {
          name: 'Elite Event Organizer',
          email: '<EMAIL>',
          password: hashedPassword,
          role: 'ORGANIZER',
          emailVerified: new Date(),
          subscriptionTier: 'PREMIUM'
        }
      });
      console.log(`Created organizer: ${newOrganizer.email}`);
    }

    const eventOrganizer = organizer || await prisma.user.findFirst({ where: { role: 'ORGANIZER' } });

    const sampleEvents = [
      {
        title: 'Tech Innovation Summit 2024',
        description: 'Join industry leaders and innovators for a day of cutting-edge technology discussions, networking, and collaboration opportunities.',
        location: 'San Francisco Convention Center',
        venue: 'Main Auditorium',
        startDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000 + 8 * 60 * 60 * 1000), // 8 hours later
        startTime: '09:00 AM',
        endTime: '05:00 PM',
        eventType: 'PHYSICAL' as const,
        category: 'TECHNOLOGY' as const,
        status: 'Published' as const,
        capacity: 500,
        hasStadiumSeating: false,
        published: true
      },
      {
        title: 'Global Finance & Fintech Conference',
        description: 'Explore the future of finance with blockchain, AI, and digital transformation. Network with financial professionals and fintech entrepreneurs.',
        location: 'New York Financial District',
        venue: 'Finance Tower Conference Center',
        startDate: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000), // 45 days from now
        endDate: new Date(Date.now() + 47 * 24 * 60 * 60 * 1000), // 2 days later
        startTime: '08:00 AM',
        endTime: '06:00 PM',
        eventType: 'PHYSICAL',
        category: 'BUSINESS',
        status: 'Published',
        capacity: 300,
        hasStadiumSeating: false,
        published: true
      }
    ];

    const createdEvents = [];
    for (const eventData of sampleEvents) {
      const event = await prisma.event.create({
        data: {
          ...eventData,
          userId: eventOrganizer!.id
        }
      });
      createdEvents.push(event);
      console.log(`Created event: ${eventData.title}`);
    }

    // Create Elite Communication subscriptions and attendee profiles
    console.log('Creating Elite Communication subscriptions and attendee profiles...');

    const createdProfiles = [];
    for (let i = 0; i < createdUsers.length; i++) {
      const user = createdUsers[i];
      const userData = user.userData;

      // Create Elite Communication subscription for each event
      for (const event of createdEvents) {
        const eliteCommunication = await prisma.eliteCommunication.create({
          data: {
            userId: user.id,
            eventId: event.id,
            tier: userData.tier as any,
            subscriptionType: 'PER_EVENT',
            isActive: true,
            expiresAt: new Date(event.endDate.getTime() + 7 * 24 * 60 * 60 * 1000), // 7 days after event
            purchasePrice: userData.tier === 'BASIC' ? 0 : userData.tier === 'ELITE' ? 29.99 : 49.99
          }
        });

        // Create attendee profile
        const attendeeProfile = await prisma.attendeeProfile.create({
          data: {
            userId: user.id,
            eventId: event.id,
            displayName: userData.name,
            bio: userData.bio,
            company: userData.company,
            role: userData.role,
            industry: userData.industry,
            interests: userData.interests,
            networkingGoals: userData.networkingGoals,
            profilePhoto: user.image,
            linkedinUrl: userData.linkedinUrl,
            isDiscoverable: userData.tier !== 'BASIC',
            privacyLevel: userData.tier === 'BASIC' ? 'HIDDEN' : userData.tier === 'ELITE' ? 'ELITE_ONLY' : 'PUBLIC',
            allowMessages: userData.tier === 'BASIC' ? 'NONE' : userData.tier === 'ELITE' ? 'ELITE_ONLY' : 'EVERYONE',
            allowMeetings: userData.tier !== 'BASIC',
            timezone: userData.timezone,
            availableHours: {
              monday: { start: '09:00', end: '17:00', available: true },
              tuesday: { start: '09:00', end: '17:00', available: true },
              wednesday: { start: '09:00', end: '17:00', available: true },
              thursday: { start: '09:00', end: '17:00', available: true },
              friday: { start: '09:00', end: '17:00', available: true },
              saturday: { start: '10:00', end: '14:00', available: false },
              sunday: { start: '10:00', end: '14:00', available: false }
            }
          }
        });

        createdProfiles.push({ profile: attendeeProfile, user, event, tier: userData.tier });
      }
    }

    console.log(`Created ${createdProfiles.length} attendee profiles with Elite Communication subscriptions`);

    // Create chat rooms for each event
    console.log('Creating Elite chat rooms...');

    const createdChatRooms = [];
    for (const event of createdEvents) {
      // Create Elite exclusive chat room
      const eliteRoom = await prisma.chatRoom.create({
        data: {
          eventId: event.id,
          name: `Elite Networking - ${event.title}`,
          description: 'Exclusive networking space for Elite and Elite Pro members',
          roomType: 'ELITE_EXCLUSIVE',
          isActive: true,
          maxMembers: 100,
          createdById: eventOrganizer!.id
        }
      });

      // Create Elite Pro exclusive chat room
      const eliteProRoom = await prisma.chatRoom.create({
        data: {
          eventId: event.id,
          name: `Elite Pro VIP Lounge - ${event.title}`,
          description: 'VIP networking space exclusively for Elite Pro members',
          roomType: 'ELITE_PRO_EXCLUSIVE',
          isActive: true,
          maxMembers: 50,
          createdById: eventOrganizer!.id
        }
      });

      createdChatRooms.push({ elite: eliteRoom, elitePro: eliteProRoom, event });
      console.log(`Created chat rooms for event: ${event.title}`);
    }

    // Add members to chat rooms based on their tier
    console.log('Adding members to chat rooms...');

    for (const roomData of createdChatRooms) {
      const eventProfiles = createdProfiles.filter(p => p.event.id === roomData.event.id);

      // Add Elite and Elite Pro members to Elite room
      const eliteMembers = eventProfiles.filter(p => p.tier === 'ELITE' || p.tier === 'ELITE_PRO');
      for (const member of eliteMembers) {
        await prisma.chatRoomMember.create({
          data: {
            chatRoomId: roomData.elite.id,
            userId: member.user.id,
            joinedAt: new Date(),
            isModerator: false,
            isMuted: false
          }
        });
      }

      // Add only Elite Pro members to Elite Pro room
      const eliteProMembers = eventProfiles.filter(p => p.tier === 'ELITE_PRO');
      for (const member of eliteProMembers) {
        await prisma.chatRoomMember.create({
          data: {
            chatRoomId: roomData.elitePro.id,
            userId: member.user.id,
            joinedAt: new Date(),
            isModerator: false,
            isMuted: false
          }
        });
      }
    }

    // Create sample messages between users
    console.log('Creating sample messages...');

    const sampleMessages = [
      {
        senderEmail: '<EMAIL>',
        receiverEmail: '<EMAIL>',
        content: 'Hi Marcus! I noticed your interest in fintech startups. I\'m working on an AI-powered financial analytics platform and would love to discuss potential collaboration opportunities.',
        eventIndex: 0
      },
      {
        senderEmail: '<EMAIL>',
        receiverEmail: '<EMAIL>',
        content: 'Hi Sarah! That sounds fascinating. I\'d definitely be interested in learning more about your platform. Are you available for a quick call this week?',
        eventIndex: 0
      },
      {
        senderEmail: '<EMAIL>',
        receiverEmail: '<EMAIL>',
        content: 'Jennifer, your background in data science is impressive! We\'re looking to implement predictive analytics in our healthcare platform. Would you be open to discussing a potential consulting opportunity?',
        eventIndex: 1
      },
      {
        senderEmail: '<EMAIL>',
        receiverEmail: '<EMAIL>',
        content: 'Lisa, I saw your presentation on growth strategies. Our agency is working with several startups that could benefit from your expertise. Let\'s connect!',
        eventIndex: 1
      }
    ];

    for (const messageData of sampleMessages) {
      const sender = createdUsers.find(u => u.email === messageData.senderEmail);
      const receiver = createdUsers.find(u => u.email === messageData.receiverEmail);
      const event = createdEvents[messageData.eventIndex];

      if (sender && receiver && event) {
        const senderProfile = createdProfiles.find(p => p.user.id === sender.id && p.event.id === event.id);
        const receiverProfile = createdProfiles.find(p => p.user.id === receiver.id && p.event.id === event.id);

        if (senderProfile && receiverProfile) {
          await prisma.message.create({
            data: {
              senderId: senderProfile.profile.id,
              receiverId: receiverProfile.profile.id,
              eventId: event.id,
              content: messageData.content,
              messageType: 'TEXT',
              isRead: Math.random() > 0.5,
              readAt: Math.random() > 0.5 ? new Date() : null
            }
          });
        }
      }
    }

    // Create sample meeting requests
    console.log('Creating sample meeting requests...');

    const sampleMeetingRequests = [
      {
        senderEmail: '<EMAIL>',
        receiverEmail: '<EMAIL>',
        title: 'AI Fintech Collaboration Discussion',
        description: 'Let\'s discuss potential collaboration opportunities between our AI platform and your fintech investments.',
        meetingType: 'VIRTUAL',
        eventIndex: 0
      },
      {
        senderEmail: '<EMAIL>',
        receiverEmail: '<EMAIL>',
        title: 'Healthcare Analytics Consulting',
        description: 'Exploring data science consulting opportunities for our healthcare platform.',
        meetingType: 'VIRTUAL',
        eventIndex: 1
      }
    ];

    for (const meetingData of sampleMeetingRequests) {
      const sender = createdUsers.find(u => u.email === meetingData.senderEmail);
      const receiver = createdUsers.find(u => u.email === meetingData.receiverEmail);
      const event = createdEvents[meetingData.eventIndex];

      if (sender && receiver && event) {
        const senderProfile = createdProfiles.find(p => p.user.id === sender.id && p.event.id === event.id);
        const receiverProfile = createdProfiles.find(p => p.user.id === receiver.id && p.event.id === event.id);

        if (senderProfile && receiverProfile) {
          const proposedStartTime = new Date(event.startDate.getTime() + Math.random() * 7 * 24 * 60 * 60 * 1000);
          const proposedEndTime = new Date(proposedStartTime.getTime() + 60 * 60 * 1000); // 1 hour meeting

          await prisma.meetingRequest.create({
            data: {
              senderId: senderProfile.profile.id,
              receiverId: receiverProfile.profile.id,
              eventId: event.id,
              title: meetingData.title,
              description: meetingData.description,
              proposedStartTime,
              proposedEndTime,
              timezone: senderProfile.user.userData.timezone,
              meetingType: meetingData.meetingType as any,
              meetingUrl: meetingData.meetingType === 'VIRTUAL' ? 'https://meet.google.com/sample-meeting' : null,
              location: meetingData.meetingType === 'IN_PERSON' ? event.location : null,
              status: Math.random() > 0.5 ? 'PENDING' : 'ACCEPTED'
            }
          });
        }
      }
    }

    console.log('Elite Communication System seeding completed successfully!');
    console.log(`Created:
    - ${createdUsers.length} sample users
    - ${createdEvents.length} sample events
    - ${createdProfiles.length} attendee profiles with Elite subscriptions
    - ${createdChatRooms.length * 2} chat rooms (Elite and Elite Pro)
    - ${sampleMessages.length} direct messages
    - ${sampleMeetingRequests.length} meeting requests`);

  } catch (error) {
    console.error('Error seeding Elite Communication System:', error);
  }
}

main()
  .catch((e) => {
    console.error('Error during database seed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
